"use client"

import React, { useState, useEffect } from 'react';
import { apiClient, App } from '@/lib/dyad-client';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Save, 
  RotateCcw, 
  FileText, 
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react';

interface DyadCodeEditorProps {
  selectedApp?: App;
  selectedFile?: string;
}

export default function DyadCodeEditor({ selectedApp, selectedFile }: DyadCodeEditorProps) {
  const [fileContent, setFileContent] = useState('');
  const [originalContent, setOriginalContent] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
  const [error, setError] = useState<string | null>(null);

  // Load file content when file changes
  useEffect(() => {
    if (selectedApp && selectedFile) {
      loadFileContent();
    } else {
      setFileContent('');
      setOriginalContent('');
      setError(null);
    }
  }, [selectedApp, selectedFile]);

  const loadFileContent = async () => {
    if (!selectedApp || !selectedFile || selectedFile === 'undefined') {
      console.warn('Invalid file selection:', { selectedApp: selectedApp?.id, selectedFile });
      return;
    }

    // Check if the selected file is actually a directory (ends with / or has no extension and common directory names)
    const isLikelyDirectory = selectedFile.endsWith('/') ||
      (!selectedFile.includes('.') && ['src', 'components', 'pages', 'lib', 'hooks', 'styles', 'public', 'node_modules'].includes(selectedFile.split('/').pop() || ''));

    if (isLikelyDirectory) {
      console.warn('Attempted to load directory as file:', selectedFile);
      setError('Cannot display directory content');
      setFileContent('');
      setOriginalContent('');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const content = await apiClient.readAppFile(selectedApp.id, selectedFile);
      setFileContent(content);
      setOriginalContent(content);
    } catch (err) {
      console.error('Failed to load file:', err);

      // Check if it's a directory error
      if (err instanceof Error && err.message.includes('Path is a directory')) {
        setError('Cannot display directory content');
        setFileContent('');
        setOriginalContent('');
      } else {
        setError('Failed to load file content');
        // Show mock content for demo
        const mockContent = getMockFileContent(selectedFile);
        setFileContent(mockContent);
        setOriginalContent(mockContent);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const getMockFileContent = (filePath: string): string => {
    const ext = filePath.split('.').pop()?.toLowerCase();
    
    switch (ext) {
      case 'tsx':
        return `import React from 'react';

interface Props {
  title: string;
  onClick: () => void;
}

export default function Component({ title, onClick }: Props) {
  return (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">{title}</h2>
      <button 
        onClick={onClick}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Click me
      </button>
    </div>
  );
}`;
      case 'css':
        return `.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.button:hover {
  background: #2563eb;
}`;
      case 'json':
        return `{
  "name": "my-app",
  "version": "1.0.0",
  "description": "A sample application",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js",
    "build": "webpack --mode production"
  },
  "dependencies": {
    "react": "^18.0.0",
    "react-dom": "^18.0.0"
  },
  "devDependencies": {
    "typescript": "^4.9.0",
    "@types/react": "^18.0.0"
  }
}`;
      case 'md':
        return `# ${filePath.split('/').pop()?.replace('.md', '') || 'README'}

This is a sample markdown file for the project.

## Features

- Feature 1: Description of feature 1
- Feature 2: Description of feature 2
- Feature 3: Description of feature 3

## Installation

\`\`\`bash
npm install
npm start
\`\`\`

## Usage

\`\`\`javascript
import Component from './Component';

function App() {
  return <Component title="Hello World" onClick={() => alert('Clicked!')} />;
}
\`\`\``;
      default:
        return `// ${selectedFile}
// This is a sample file

console.log('Hello from ${selectedFile}');`;
    }
  };

  const saveFile = async () => {
    if (!selectedApp || !selectedFile) return;

    try {
      setIsSaving(true);
      setSaveStatus('saving');
      
      await apiClient.writeAppFile(selectedApp.id, selectedFile, fileContent);
      setOriginalContent(fileContent);
      setSaveStatus('saved');
      
      // Reset status after 2 seconds
      setTimeout(() => setSaveStatus('idle'), 2000);
    } catch (err) {
      console.error('Failed to save file:', err);
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  const revertChanges = () => {
    setFileContent(originalContent);
    setSaveStatus('idle');
  };

  const hasChanges = fileContent !== originalContent;

  const getLanguageFromFile = (filePath: string): string => {
    const ext = filePath.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'tsx':
      case 'ts':
        return 'typescript';
      case 'jsx':
      case 'js':
        return 'javascript';
      case 'css':
        return 'css';
      case 'html':
        return 'html';
      case 'json':
        return 'json';
      case 'md':
        return 'markdown';
      case 'py':
        return 'python';
      default:
        return 'text';
    }
  };

  const getSaveStatusIcon = () => {
    switch (saveStatus) {
      case 'saving':
        return <Loader2 className="w-3 h-3 animate-spin text-blue-500" />;
      case 'saved':
        return <CheckCircle className="w-3 h-3 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-3 h-3 text-red-500" />;
      default:
        return null;
    }
  };

  const getSaveStatusText = () => {
    switch (saveStatus) {
      case 'saving':
        return 'Saving...';
      case 'saved':
        return 'Saved';
      case 'error':
        return 'Save failed';
      default:
        return hasChanges ? 'Unsaved changes' : 'No changes';
    }
  };

  if (!selectedApp || !selectedFile) {
    return (
      <div className="flex-1 bg-[#0a0a0a] flex items-center justify-center">
        <div className="text-center text-[#666] p-8">
          <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-medium mb-2">No file selected</h3>
          <p className="text-sm">Select a file from the explorer to start editing</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 bg-[#0a0a0a] flex flex-col">
      {/* File Header */}
      <div className="h-12 bg-[#111111] border-b border-[#1a1a1a] flex items-center justify-between px-4">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <FileText className="w-4 h-4 text-blue-500" />
            <span className="text-sm text-white font-medium">{selectedFile}</span>
            {hasChanges && (
              <div className="w-2 h-2 bg-orange-500 rounded-full" />
            )}
          </div>
          <Badge 
            variant="secondary" 
            className="bg-[#2a2a2a] text-[#888] text-[10px] px-2 py-0"
          >
            {getLanguageFromFile(selectedFile)}
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1 text-xs text-[#666]">
            {getSaveStatusIcon()}
            <span>{getSaveStatusText()}</span>
          </div>
          
          {hasChanges && (
            <Button
              size="sm"
              variant="ghost"
              onClick={revertChanges}
              className="h-7 px-2 text-[#888] hover:text-white hover:bg-[#1a1a1a]"
            >
              <RotateCcw className="w-3 h-3 mr-1" />
              Revert
            </Button>
          )}
          
          <Button
            size="sm"
            onClick={saveFile}
            disabled={!hasChanges || isSaving}
            className="h-7 px-3 bg-blue-600 hover:bg-blue-700 disabled:bg-[#333] disabled:text-[#666]"
          >
            <Save className="w-3 h-3 mr-1" />
            Save
          </Button>
        </div>
      </div>

      {/* Editor Content */}
      <div className="flex-1 relative">
        {isLoading ? (
          <div className="absolute inset-0 flex items-center justify-center bg-[#0a0a0a]">
            <div className="text-center text-[#666]">
              <Loader2 className="w-6 h-6 animate-spin mx-auto mb-2" />
              <p className="text-sm">Loading file...</p>
            </div>
          </div>
        ) : error ? (
          <div className="absolute inset-0 flex items-center justify-center bg-[#0a0a0a]">
            <div className="text-center text-red-400 p-8">
              <AlertCircle className="w-8 h-8 mx-auto mb-2" />
              <p className="text-sm">{error}</p>
              <Button
                size="sm"
                variant="outline"
                onClick={loadFileContent}
                className="mt-3 bg-[#1a1a1a] border-[#333] text-white hover:bg-[#2a2a2a]"
              >
                Retry
              </Button>
            </div>
          </div>
        ) : (
          <textarea
            value={fileContent}
            onChange={(e) => setFileContent(e.target.value)}
            className="w-full h-full bg-[#0a0a0a] text-[#e5e5e5] font-mono text-sm p-4 resize-none focus:outline-none border-none"
            style={{
              fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
              lineHeight: '1.5',
              tabSize: 2,
            }}
            placeholder="Start typing..."
            spellCheck={false}
          />
        )}
      </div>
    </div>
  );
}
