@echo off
echo ========================================
echo    Starting Dyad Frontend + Backend
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not installed or not in PATH
    pause
    exit /b 1
)

echo Node.js and npm are available
echo.

REM Set window title
title Dyad Launcher

REM Create log directory if it doesn't exist
if not exist "logs" mkdir logs

echo Starting Backend (Dyad Main App)...
echo Backend will run in a new window
echo.

REM Start backend in new window
start "Dyad Backend" cmd /k "echo Starting Dyad Backend... && npm start && echo Backend stopped. Press any key to close. && pause"

echo Waiting 3 seconds for backend to initialize...
timeout /t 3 /nobreak >nul

echo.
echo Starting Frontend (AI Dev Ecosystem)...
echo Frontend will run in a new window
echo.

REM Start frontend in new window
start "Dyad Frontend" cmd /k "echo Starting Dyad Frontend... && cd ai-dev-ecosystem && npm run dev && echo Frontend stopped. Press any key to close. && pause"

echo.
echo ========================================
echo    Both services are starting up!
echo ========================================
echo.
echo Backend (Dyad): Starting in separate window
echo Frontend (Web UI): Starting in separate window (http://localhost:3001)
echo.
echo Wait a few moments for both services to fully start.
echo The frontend will be available at: http://localhost:3001
echo.
echo To stop the services:
echo - Close the Backend window (Dyad Backend)
echo - Close the Frontend window (Dyad Frontend)
echo.
echo Press any key to exit this launcher...
pause >nul
