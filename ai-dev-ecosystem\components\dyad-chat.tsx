"use client"

import React, { useState, useEffect, useRef } from 'react';
import { useDyadChats, useDyadStreaming, useDyadModels } from '@/hooks/use-dyad';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowUp, 
  Bot, 
  User, 
  Loader2, 
  MessageSquare,
  Settings,
  Plus,
  Trash2
} from 'lucide-react';
import { App, Chat, ChatMessage } from '@/lib/dyad-client';

interface DyadChatProps {
  selectedApp?: App;
}

export default function DyadChat({ selectedApp }: DyadChatProps) {
  const { chats, createChat, deleteChat, addMessage } = useDyadChats(selectedApp?.id);
  const { models } = useDyadModels();
  const { isStreaming, streamContent, startStream, resetStream } = useDyadStreaming();
  
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [chatInput, setChatInput] = useState('');
  const [selectedModel, setSelectedModel] = useState('gpt-4');
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatMessages, streamContent]);

  // Load messages when chat is selected
  useEffect(() => {
    if (selectedChat) {
      loadChatMessages(selectedChat.id);
    }
  }, [selectedChat]);

  // Auto-select first chat when chats load
  useEffect(() => {
    if (chats.length > 0 && !selectedChat) {
      setSelectedChat(chats[0]);
    }
  }, [chats, selectedChat]);

  const loadChatMessages = async (chatId: number) => {
    try {
      setIsLoadingMessages(true);
      // In a real implementation, you'd fetch messages for the specific chat
      // For now, we'll use the messages from the chat object if available
      const chat = chats.find(c => c.id === chatId);
      if (chat?.messages) {
        setChatMessages(chat.messages);
      }
    } catch (err) {
      console.error('Failed to load messages:', err);
    } finally {
      setIsLoadingMessages(false);
    }
  };

  const handleCreateChat = async () => {
    if (!selectedApp) return;
    
    try {
      const newChat = await createChat(selectedApp.id, `Chat ${chats.length + 1}`);
      setSelectedChat(newChat);
      setChatMessages([]);
    } catch (err) {
      console.error('Failed to create chat:', err);
    }
  };

  const handleDeleteChat = async (chat: Chat) => {
    try {
      await deleteChat(chat.id);
      if (selectedChat?.id === chat.id) {
        setSelectedChat(null);
        setChatMessages([]);
      }
    } catch (err) {
      console.error('Failed to delete chat:', err);
    }
  };

  const handleSendMessage = async () => {
    if (!chatInput.trim() || !selectedChat || !selectedApp) return;

    const userMessage = chatInput;
    setChatInput('');
    resetStream();

    // Add user message to UI immediately
    const tempUserMessage: ChatMessage = {
      id: Date.now(),
      chatId: selectedChat.id,
      content: userMessage,
      role: 'user',
      createdAt: new Date().toISOString(),
    };
    setChatMessages(prev => [...prev, tempUserMessage]);

    try {
      // Add user message to backend
      await addMessage(selectedChat.id, userMessage, 'user');

      // Start streaming AI response
      await startStream(
        selectedChat.id,
        userMessage,
        { name: selectedModel, provider: 'openrouter' },
        selectedApp.id
      );

      // Add AI response to messages when streaming is complete
      if (streamContent) {
        const aiMessage: ChatMessage = {
          id: Date.now() + 1,
          chatId: selectedChat.id,
          content: streamContent,
          role: 'assistant',
          createdAt: new Date().toISOString(),
        };
        setChatMessages(prev => [...prev, aiMessage]);
      }
    } catch (err) {
      console.error('Failed to send message:', err);
    }
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!selectedApp) {
    return (
      <div className="h-full flex items-center justify-center text-[#666]">
        <div className="text-center">
          <MessageSquare className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>Select an app to start chatting</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-[#0a0a0a] rounded-xl overflow-hidden">
      {/* Chat Header */}
      <div className="px-6 py-4 border-b border-[#1a1a1a] flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <h2 className="font-medium text-white text-sm">
            {selectedApp.name} - Chat
          </h2>
          {selectedChat && (
            <Badge variant="secondary" className="bg-[#1a1a1a] text-[#888] text-xs">
              {selectedChat.title || `Chat ${selectedChat.id}`}
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={handleCreateChat}
            className="h-7 px-2 bg-[#1a1a1a] border-[#333] text-[#888] hover:text-white hover:bg-[#2a2a2a]"
          >
            <Plus className="w-3 h-3 mr-1" />
            New
          </Button>
          <Select value={selectedModel} onValueChange={setSelectedModel}>
            <SelectTrigger className="w-40 h-7 bg-[#1a1a1a] border-[#333] text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-[#1a1a1a] border-[#333]">
              {models.slice(0, 5).map((model) => (
                <SelectItem key={model.name} value={model.name} className="text-xs">
                  {model.displayName || model.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Chat Messages */}
      <div className="flex-1 min-h-0 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="px-6 py-4 space-y-4">
            {isLoadingMessages ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="w-4 h-4 animate-spin text-[#666]" />
              </div>
            ) : chatMessages.length === 0 ? (
              <div className="text-center py-8 text-[#666]">
                <Bot className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Start a conversation with your AI assistant</p>
              </div>
            ) : (
              chatMessages.map((message) => (
                <div key={message.id} className="group">
                  <div className={`flex gap-3 ${message.role === 'user' ? 'flex-row-reverse' : ''}`}>
                    <div className="flex-shrink-0">
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                          message.role === 'user'
                            ? 'bg-blue-600 text-white'
                            : 'bg-[#1a1a1a] text-[#888] border border-[#2a2a2a]'
                        }`}
                      >
                        {message.role === 'user' ? (
                          <User className="w-4 h-4" />
                        ) : (
                          <Bot className="w-4 h-4" />
                        )}
                      </div>
                    </div>
                    <div className={`flex-1 max-w-[85%] ${message.role === 'user' ? 'text-right' : ''}`}>
                      <div
                        className={`inline-block px-4 py-3 rounded-lg text-sm leading-relaxed ${
                          message.role === 'user'
                            ? 'bg-blue-600 text-white shadow-lg'
                            : 'bg-[#111111] text-[#e5e5e5] shadow-md'
                        }`}
                      >
                        <div className="whitespace-pre-wrap">{message.content}</div>
                      </div>
                      <div className={`text-xs text-[#666] mt-1 ${message.role === 'user' ? 'text-right' : ''}`}>
                        {formatTime(message.createdAt)}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}

            {/* Streaming Response */}
            {isStreaming && streamContent && (
              <div className="group">
                <div className="flex gap-3">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium bg-[#1a1a1a] text-[#888] border border-[#2a2a2a]">
                      <Bot className="w-4 h-4" />
                    </div>
                  </div>
                  <div className="flex-1 max-w-[85%]">
                    <div className="inline-block px-4 py-3 rounded-lg text-sm leading-relaxed bg-[#111111] text-[#e5e5e5] shadow-md">
                      <div className="whitespace-pre-wrap">{streamContent}</div>
                      <div className="inline-block w-2 h-4 bg-blue-500 animate-pulse ml-1"></div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
      </div>

      {/* Chat Input */}
      <div className="shrink-0 p-3 bg-[#0a0a0a] shadow-inner">
        <div className="relative bg-[#111111] rounded-xl p-1 focus-within:shadow-blue-500/20 focus-within:shadow-lg transition-all shadow-md">
          <textarea
            ref={textareaRef}
            value={chatInput}
            onChange={(e) => {
              setChatInput(e.target.value);
              // Auto-resize textarea
              const textarea = e.target as HTMLTextAreaElement;
              textarea.style.height = 'auto';
              const scrollHeight = textarea.scrollHeight;
              const maxHeight = 240; // 10 lines * 24px line height
              textarea.style.height = Math.min(scrollHeight, maxHeight) + 'px';
            }}
            onKeyDown={handleKeyDown}
            placeholder={`Message ${selectedApp.name}...`}
            className="w-full bg-transparent px-4 py-3 pr-12 text-sm text-white placeholder:text-[#666] resize-none focus:outline-none leading-6 scrollbar-none overflow-y-auto"
            rows={3}
            style={{
              minHeight: '84px',
              maxHeight: '240px'
            }}
            disabled={isStreaming}
          />
          <button
            onClick={handleSendMessage}
            disabled={!chatInput.trim() || isStreaming}
            className="absolute right-2 top-3 w-8 h-8 bg-blue-600 hover:bg-blue-700 disabled:bg-[#333] disabled:text-[#666] rounded-lg flex items-center justify-center transition-colors shadow-md"
          >
            {isStreaming ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <ArrowUp className="w-4 h-4" />
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
