"use client"

import React, { useState, useEffect } from 'react';
import { apiClient, App } from '@/lib/dyad-client';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Folder, 
  File, 
  ChevronRight, 
  ChevronDown, 
  Plus, 
  Search,
  FileText,
  FileCode,
  Image,
  Settings
} from 'lucide-react';

interface FileNode {
  name: string;
  path: string;
  type: 'file' | 'directory';
  children?: FileNode[];
  isExpanded?: boolean;
}

interface DyadFileBrowserProps {
  selectedApp?: App;
  onFileSelect: (filePath: string) => void;
  selectedFile?: string;
}

export default function DyadFileBrowser({ selectedApp, onFileSelect, selectedFile }: DyadFileBrowserProps) {
  const [fileTree, setFileTree] = useState<FileNode[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredTree, setFilteredTree] = useState<FileNode[]>([]);

  // Load file tree when app changes
  useEffect(() => {
    if (selectedApp) {
      loadFileTree();
    }
  }, [selectedApp]);

  // Filter tree based on search
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredTree(fileTree);
    } else {
      const filtered = filterTree(fileTree, searchQuery.toLowerCase());
      setFilteredTree(filtered);
    }
  }, [fileTree, searchQuery]);

  const loadFileTree = async () => {
    if (!selectedApp) return;

    try {
      setIsLoading(true);
      // Get app files from Dyad backend
      const files = await apiClient.getAppFiles(selectedApp.id);
      const tree = buildFileTree(files);
      setFileTree(tree);
    } catch (err) {
      console.error('Failed to load file tree:', err);
      // Fallback to mock data for demo
      setFileTree(getMockFileTree());
    } finally {
      setIsLoading(false);
    }
  };

  const buildFileTree = (files: string[]): FileNode[] => {
    const tree: FileNode[] = [];
    const nodeMap = new Map<string, FileNode>();

    files.forEach(filePath => {
      const parts = filePath.split('/').filter(Boolean);
      let currentPath = '';

      parts.forEach((part, index) => {
        const parentPath = currentPath;
        currentPath = currentPath ? `${currentPath}/${part}` : part;
        
        if (!nodeMap.has(currentPath)) {
          const node: FileNode = {
            name: part,
            path: currentPath,
            type: index === parts.length - 1 ? 'file' : 'directory',
            children: index === parts.length - 1 ? undefined : [],
            isExpanded: false,
          };

          nodeMap.set(currentPath, node);

          if (parentPath) {
            const parent = nodeMap.get(parentPath);
            if (parent && parent.children) {
              parent.children.push(node);
            }
          } else {
            tree.push(node);
          }
        }
      });
    });

    return tree;
  };

  const getMockFileTree = (): FileNode[] => [
    {
      name: 'src',
      path: 'src',
      type: 'directory',
      isExpanded: true,
      children: [
        {
          name: 'components',
          path: 'src/components',
          type: 'directory',
          isExpanded: false,
          children: [
            { name: 'Calculator.tsx', path: 'src/components/Calculator.tsx', type: 'file' },
            { name: 'Button.tsx', path: 'src/components/Button.tsx', type: 'file' },
          ]
        },
        { name: 'App.tsx', path: 'src/App.tsx', type: 'file' },
        { name: 'index.css', path: 'src/index.css', type: 'file' },
        { name: 'main.tsx', path: 'src/main.tsx', type: 'file' },
      ]
    },
    {
      name: 'public',
      path: 'public',
      type: 'directory',
      isExpanded: false,
      children: [
        { name: 'index.html', path: 'public/index.html', type: 'file' },
        { name: 'favicon.ico', path: 'public/favicon.ico', type: 'file' },
      ]
    },
    { name: 'package.json', path: 'package.json', type: 'file' },
    { name: 'vite.config.ts', path: 'vite.config.ts', type: 'file' },
    { name: 'README.md', path: 'README.md', type: 'file' },
  ];

  const filterTree = (nodes: FileNode[], query: string): FileNode[] => {
    return nodes.reduce((filtered: FileNode[], node) => {
      if (node.name.toLowerCase().includes(query)) {
        filtered.push({ ...node, isExpanded: true });
      } else if (node.children) {
        const filteredChildren = filterTree(node.children, query);
        if (filteredChildren.length > 0) {
          filtered.push({
            ...node,
            children: filteredChildren,
            isExpanded: true,
          });
        }
      }
      return filtered;
    }, []);
  };

  const toggleExpanded = (path: string) => {
    const updateTree = (nodes: FileNode[]): FileNode[] => {
      return nodes.map(node => {
        if (node.path === path) {
          return { ...node, isExpanded: !node.isExpanded };
        }
        if (node.children) {
          return { ...node, children: updateTree(node.children) };
        }
        return node;
      });
    };

    setFileTree(updateTree(fileTree));
  };

  const getFileIcon = (fileName: string, type: 'file' | 'directory') => {
    if (type === 'directory') {
      return <Folder className="w-4 h-4 text-blue-500" />;
    }

    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'tsx':
      case 'ts':
      case 'js':
      case 'jsx':
        return <FileCode className="w-4 h-4 text-blue-400" />;
      case 'css':
      case 'scss':
      case 'less':
        return <FileCode className="w-4 h-4 text-pink-400" />;
      case 'html':
        return <FileCode className="w-4 h-4 text-orange-400" />;
      case 'json':
        return <Settings className="w-4 h-4 text-yellow-400" />;
      case 'md':
        return <FileText className="w-4 h-4 text-gray-400" />;
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'svg':
        return <Image className="w-4 h-4 text-green-400" />;
      default:
        return <File className="w-4 h-4 text-gray-400" />;
    }
  };

  const renderFileNode = (node: FileNode, depth = 0) => {
    const isSelected = selectedFile === node.path;
    
    return (
      <div key={node.path}>
        <div
          className={`flex items-center gap-2 py-1 px-2 hover:bg-[#1a1a1a] cursor-pointer transition-colors ${
            isSelected ? 'bg-[#1a1a1a] border-l-2 border-blue-500' : ''
          }`}
          style={{ paddingLeft: `${8 + depth * 16}px` }}
          onClick={() => {
            if (node.type === 'directory') {
              toggleExpanded(node.path);
            } else {
              onFileSelect(node.path);
            }
          }}
        >
          {node.type === 'directory' && (
            <div className="w-4 h-4 flex items-center justify-center">
              {node.isExpanded ? (
                <ChevronDown className="w-3 h-3 text-[#666]" />
              ) : (
                <ChevronRight className="w-3 h-3 text-[#666]" />
              )}
            </div>
          )}
          {node.type === 'file' && <div className="w-4" />}
          {getFileIcon(node.name, node.type)}
          <span className={`text-xs ${isSelected ? 'text-white font-medium' : 'text-[#e5e5e5]'}`}>
            {node.name}
          </span>
        </div>
        {node.type === 'directory' && node.isExpanded && node.children && (
          <div>
            {node.children.map(child => renderFileNode(child, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="w-64 bg-[#0a0a0a] border-r border-[#1a1a1a] flex flex-col h-full">
      {/* Header */}
      <div className="px-4 py-3 border-b border-[#1a1a1a] flex items-center justify-between">
        <h3 className="text-sm font-medium text-white">Explorer</h3>
        <Button
          size="sm"
          variant="ghost"
          className="h-6 w-6 p-0 text-[#666] hover:text-white hover:bg-[#1a1a1a]"
        >
          <Plus className="w-3 h-3" />
        </Button>
      </div>

      {/* Search */}
      <div className="p-3 border-b border-[#1a1a1a]">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-3 h-3 text-[#666]" />
          <Input
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search files..."
            className="pl-9 h-7 bg-[#111111] border-[#333] text-white text-xs placeholder:text-[#666]"
          />
        </div>
      </div>

      {/* File Tree */}
      <ScrollArea className="flex-1">
        <div className="p-2">
          {!selectedApp ? (
            <div className="text-center py-8 text-[#666]">
              <Folder className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-xs">Select an app to view files</p>
            </div>
          ) : isLoading ? (
            <div className="text-center py-8 text-[#666]">
              <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2" />
              <p className="text-xs">Loading files...</p>
            </div>
          ) : filteredTree.length === 0 ? (
            <div className="text-center py-8 text-[#666]">
              <File className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-xs">
                {searchQuery ? 'No files found' : 'No files in this app'}
              </p>
            </div>
          ) : (
            <div className="space-y-0">
              {filteredTree.map(node => renderFileNode(node))}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
}
