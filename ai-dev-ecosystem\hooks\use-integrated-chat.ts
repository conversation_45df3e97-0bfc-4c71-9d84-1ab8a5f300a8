import { useState, useEffect, useCallback } from 'react';
import { useDyadApps, useDyadChats, useDyadStreaming, useDyadModels } from '@/hooks/use-dyad';
import { apiClient, App, Chat, ChatMessage } from '@/lib/dyad-client';

// Types that match the existing UI
interface Message {
  id: string;
  type: "user" | "ai";
  content: string;
  timestamp: Date;
  tasks?: Task[];
}

interface Task {
  id: string;
  title: string;
  status: "pending" | "running" | "completed" | "error";
  type: "component" | "api" | "database" | "test";
  progress?: number;
}

export function useIntegratedChat() {
  // Dyad hooks
  const { apps, createApp } = useDyadApps();
  const { chats, createChat, deleteChat } = useDyadChats();
  const { models } = useDyadModels();
  const { isStreaming, streamContent, startStream, resetStream } = useDyadStreaming();

  // UI state
  const [selectedApp, setSelectedApp] = useState<App | null>(null);
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);

  // Auto-select first app if available
  useEffect(() => {
    if (apps.length > 0 && !selectedApp) {
      setSelectedApp(apps[0]);
    }
  }, [apps, selectedApp]);

  // Auto-create first chat when app is selected
  useEffect(() => {
    if (selectedApp && chats.length === 0) {
      handleCreateChat();
    } else if (selectedApp && chats.length > 0 && !selectedChat) {
      setSelectedChat(chats[0]);
    }
  }, [selectedApp, chats]);

  // Load messages when chat is selected
  useEffect(() => {
    if (selectedChat) {
      loadChatMessages(selectedChat);
    }
  }, [selectedChat]);

  // Convert Dyad ChatMessage to UI Message
  const convertToUIMessage = (chatMessage: ChatMessage): Message => ({
    id: chatMessage.id.toString(),
    type: chatMessage.role === 'user' ? 'user' : 'ai',
    content: chatMessage.content,
    timestamp: new Date(chatMessage.createdAt),
    // Add mock tasks for AI messages to maintain UI compatibility
    tasks: chatMessage.role === 'assistant' ? [
      {
        id: `task-${chatMessage.id}`,
        title: "Processing your request",
        status: "completed",
        type: "component",
        progress: 100,
      }
    ] : undefined,
  });

  const loadChatMessages = async (chat: Chat) => {
    try {
      setIsLoadingMessages(true);
      const fullChat = await apiClient.getChat(chat.id);
      if (fullChat.messages) {
        const uiMessages = fullChat.messages.map(convertToUIMessage);
        setMessages(uiMessages);
      }
    } catch (err) {
      console.error('Failed to load messages:', err);
      // Fallback to empty messages
      setMessages([]);
    } finally {
      setIsLoadingMessages(false);
    }
  };

  const handleCreateApp = async (name: string) => {
    try {
      const newApp = await createApp(name, 'react');
      setSelectedApp(newApp);
      return newApp;
    } catch (err) {
      console.error('Failed to create app:', err);
      throw err;
    }
  };

  const handleCreateChat = async () => {
    if (!selectedApp) {
      // Create a default app if none exists
      try {
        const newApp = await handleCreateApp('My Project');
        const newChat = await createChat(newApp.id, `Chat ${chats.length + 1}`);
        setSelectedChat(newChat);
        return newChat;
      } catch (err) {
        console.error('Failed to create app and chat:', err);
        return null;
      }
    }

    try {
      const newChat = await createChat(selectedApp.id, `Chat ${chats.length + 1}`);
      setSelectedChat(newChat);
      return newChat;
    } catch (err) {
      console.error('Failed to create chat:', err);
      return null;
    }
  };

  const handleSendMessage = async (content: string, selectedModel: string = 'gpt-4') => {
    if (!content.trim()) return;

    // Ensure we have an app and chat
    let currentApp = selectedApp;
    let currentChat = selectedChat;

    if (!currentApp) {
      currentApp = await handleCreateApp('My Project');
      if (!currentApp) return;
    }

    if (!currentChat) {
      currentChat = await handleCreateChat();
      if (!currentChat) return;
    }

    // Add user message to UI immediately
    const userMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, userMessage]);

    try {
      // Add user message to backend
      await apiClient.addMessage(currentChat.id, content, 'user');

      // Start streaming AI response
      resetStream();
      
      // Add a temporary AI message for the streaming response
      const tempAiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: "ai",
        content: "",
        timestamp: new Date(),
        tasks: [
          {
            id: Date.now().toString(),
            title: "Processing your request",
            status: "running",
            type: "component",
            progress: 30,
          },
        ],
      };
      setMessages(prev => [...prev, tempAiMessage]);

      // Start the actual stream
      await startStream(
        currentChat.id,
        content,
        { name: selectedModel, provider: 'openrouter' },
        currentApp.id
      );

    } catch (err) {
      console.error('Failed to send message:', err);
      
      // Add error message
      const errorMessage: Message = {
        id: (Date.now() + 2).toString(),
        type: "ai",
        content: "Sorry, I encountered an error processing your request. Please try again.",
        timestamp: new Date(),
        tasks: [
          {
            id: Date.now().toString(),
            title: "Error occurred",
            status: "error",
            type: "component",
          },
        ],
      };
      setMessages(prev => [...prev, errorMessage]);
    }
  };

  // Update the last AI message with streaming content
  useEffect(() => {
    if (streamContent && messages.length > 0) {
      setMessages(prev => {
        const newMessages = [...prev];
        const lastMessage = newMessages[newMessages.length - 1];
        
        if (lastMessage && lastMessage.type === 'ai') {
          lastMessage.content = streamContent;
          if (lastMessage.tasks) {
            lastMessage.tasks[0].status = isStreaming ? "running" : "completed";
            lastMessage.tasks[0].progress = isStreaming ? 75 : 100;
          }
        }
        
        return newMessages;
      });
    }
  }, [streamContent, isStreaming, messages.length]);

  const handleSelectChat = (chat: Chat) => {
    setSelectedChat(chat);
  };

  const handleDeleteChat = async (chat: Chat) => {
    try {
      await deleteChat(chat.id);
      if (selectedChat?.id === chat.id) {
        setSelectedChat(null);
        setMessages([]);
      }
    } catch (err) {
      console.error('Failed to delete chat:', err);
    }
  };

  return {
    // State
    selectedApp,
    selectedChat,
    messages,
    isStreaming,
    isLoadingMessages,
    apps,
    chats,
    models,

    // Actions
    handleSendMessage,
    handleCreateChat,
    handleSelectChat,
    handleDeleteChat,
    setSelectedApp,

    // Utilities
    formatTime: (date: Date) => date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
  };
}
