"use client"

import React from "react"
import { useState, useRef, use<PERSON><PERSON>back, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  MessageSquare,
  Settings,
  Upload,
  Eye,
  Code,
  ChevronLeft,
  ChevronRight,
  RotateCcw,
  Search,
  ArrowUp,
  User,
  Bot,
  Loader2,
  FileText,
  GitBranch,
  BarChart3,
  MessageSquarePlus,
  HelpCircle,
  Trash2,
} from "lucide-react"
import AdminDashboard from "@/components/admin-dashboard"
import TaskList from "@/components/task-list"
import StreamingPreview from "@/components/streaming-preview"
import ConversationThreads from "@/components/conversation-threads"
import DyadIntegration from "@/components/dyad-integration"
import DyadFileBrowser from "@/components/dyad-file-browser"
import DyadCodeEditor from "@/components/dyad-code-editor"
import DyadAppPreview from "@/components/dyad-app-preview"
import DyadPlanning from "@/components/dyad-planning"
import DyadGraph from "@/components/dyad-graph"
import { useIntegratedChat } from "@/hooks/use-integrated-chat"
import { useDyadChats, useDyad } from "@/hooks/use-dyad"
import { ScrollArea } from "@/components/ui/scroll-area"

interface Message {
  id: string
  type: "user" | "ai"
  content: string
  timestamp: Date
  tasks?: Task[]
}

interface Task {
  id: string
  title: string
  status: "pending" | "running" | "completed" | "error"
  type: "component" | "api" | "database" | "test"
  progress?: number
}

export default function AIDevInterface() {
  const [currentView, setCurrentView] = useState<"main" | "admin" | "dyad">("main")
  const [activeTab, setActiveTab] = useState<"preview" | "code" | "planning" | "graph">("preview")
  const [chatInput, setChatInput] = useState("")
  const [selectedModel, setSelectedModel] = useState("anthropic/claude-sonnet-4")
  const [sidebarWidth, setSidebarWidth] = useState(400) // Default width for SSR
  const [isResizing, setIsResizing] = useState(false)
  const [selectedFile, setSelectedFile] = useState<string>()
  const [isLeftPanelOpen, setIsLeftPanelOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")

  // Use integrated chat hook
  const {
    selectedApp,
    selectedChat,
    messages,
    isStreaming,
    isLoadingMessages,
    handleSendMessage,
    handleCreateChat,
    handleSelectChat: selectChat,
    formatTime,
    models,
  } = useIntegratedChat()

  // Get Dyad chats for the selected app
  const { chats: dyadChats, createChat: createDyadChat, deleteChat: deleteDyadChat } = useDyadChats(selectedApp?.id)

  // Test Dyad connection
  const { isConnected, isLoading: isDyadLoading, error: dyadError } = useDyad()

  // Set proper sidebar width after hydration
  useEffect(() => {
    if (typeof window !== "undefined") {
      const calculatedWidth = Math.floor((window.innerWidth - 48) / 3)
      setSidebarWidth(calculatedWidth)
    }
  }, [])

  const resizeRef = useRef<HTMLDivElement>(null)

  const handleSendMessageClick = async () => {
    if (!chatInput.trim()) return

    const message = chatInput
    setChatInput("")

    // Use the integrated chat handler
    await handleSendMessage(message, selectedModel)
  }

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsResizing(true)
    e.preventDefault()
  }, [])

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isResizing) return
      const newWidth = e.clientX - 16 // Account for padding
      setSidebarWidth(Math.max(300, Math.min(800, newWidth)))
    },
    [isResizing]
  )

  const handleMouseUp = useCallback(() => {
    setIsResizing(false)
  }, [])

  useEffect(() => {
    if (isResizing) {
      document.addEventListener("mousemove", handleMouseMove)
      document.addEventListener("mouseup", handleMouseUp)
      return () => {
        document.removeEventListener("mousemove", handleMouseMove)
        document.removeEventListener("mouseup", handleMouseUp)
      }
    }
  }, [isResizing, handleMouseMove, handleMouseUp])

  if (currentView === "admin") {
    return <AdminDashboard onBack={() => setCurrentView("main")} />
  }

  if (currentView === "dyad") {
    return (
      <div className="h-screen w-full bg-[#000000] text-[#e5e5e5] font-sans text-sm flex flex-col overflow-hidden">
        {/* Top Navigation Bar */}
        <header className="h-14 bg-[#0a0a0a] border-b border-[#1a1a1a] flex items-center justify-between px-6">
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-3">
              <div className="w-14 h-14 rounded-lg overflow-hidden flex items-center justify-center">
                <img
                  src="/logo.png"
                  alt="AI Dev Ecosystem"
                  className="w-full h-full object-contain"
                />
              </div>
            </div>
            <div className="flex items-center gap-1 text-xs text-[#666]">
              <span>Dyad Integration</span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-3 text-xs text-[#888] hover:text-white hover:bg-[#1a1a1a] rounded-md"
              onClick={() => setCurrentView("main")}
            >
              Back to Main
            </Button>
            <div className="w-7 h-7 bg-[#1a1a1a] rounded-full flex items-center justify-center ml-2">
              <User className="w-4 h-4 text-[#888]" />
            </div>
          </div>
        </header>

        {/* Dyad Integration Content */}
        <div className="flex-1 overflow-hidden p-6">
          <DyadIntegration />
        </div>
      </div>
    )
  }

  return (
    <div className="h-screen w-full bg-[#000000] text-[#e5e5e5] font-sans text-sm flex flex-col overflow-hidden relative">
        <ConversationThreads isVisible={false} onSelectThread={() => {}} />

        {/* Left Edge Hover Trigger */}
        <div
          className="fixed left-0 top-0 w-4 h-full z-50 bg-transparent"
          onMouseEnter={() => setIsLeftPanelOpen(true)}
        />

        {/* Left Slideout Panel */}
        <div
          className={`fixed left-0 top-0 h-full w-80 bg-[#0a0a0a] border-r border-[#1a1a1a] z-40 transform transition-transform duration-300 ease-in-out ${
            isLeftPanelOpen ? 'translate-x-0' : '-translate-x-full'
          }`}
          onMouseLeave={() => setIsLeftPanelOpen(false)}
        >
          {/* Panel Header */}
          <div className="p-6 border-b border-[#1a1a1a]">
            <div className="flex items-center gap-2 mb-4">
              <div className="text-xl font-bold">AP3<span className="text-red-500">X</span></div>
            </div>

            <button
              onClick={async () => {
                if (selectedApp) {
                  try {
                    const newChat = await createDyadChat(selectedApp.id, `Chat ${dyadChats.length + 1}`);
                    selectChat(newChat);
                    setIsLeftPanelOpen(false);
                  } catch (err) {
                    console.error('Failed to create chat:', err);
                  }
                }
              }}
              disabled={!selectedApp}
              className="w-full flex items-center gap-2 px-3 py-2 bg-[#1a1a1a] hover:bg-[#2a2a2a] disabled:opacity-50 disabled:cursor-not-allowed rounded-lg text-sm transition-colors"
            >
              <MessageSquarePlus className="w-4 h-4" />
              Start new chat
            </button>

            <div className="mt-4">
              <input
                type="text"
                placeholder="Search"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-3 py-2 bg-[#1a1a1a] border border-[#333] rounded-lg text-sm focus:outline-none focus:border-[#555]"
              />
            </div>
          </div>

          {/* Chat History */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Project Selection */}
              {selectedApp && (
                <div className="mb-4">
                  <div className="flex items-center gap-2 px-3 py-2 bg-[#1a1a1a] rounded-lg">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-xs text-white font-medium">{selectedApp.name}</span>
                    <span className="text-xs text-[#666]">• Active Project</span>
                  </div>
                </div>
              )}

              {(() => {
                // Use Dyad chats instead of integrated chats
                const allChats = dyadChats || [];

                // Filter chats based on search query
                const filteredChats = allChats.filter(chat => {
                  if (!searchQuery.trim()) return true;
                  const title = chat.title || 'Untitled Chat';
                  return title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    chat.messages?.some(msg =>
                      msg.content.toLowerCase().includes(searchQuery.toLowerCase())
                    );
                });

                // Group filtered chats by time periods
                const now = new Date();
                const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                const yesterday = new Date(today);
                yesterday.setDate(yesterday.getDate() - 1);
                const thirtyDaysAgo = new Date(today);
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

                const todayChats = filteredChats.filter(chat => {
                  const chatDate = new Date(chat.createdAt);
                  const chatDateOnly = new Date(chatDate.getFullYear(), chatDate.getMonth(), chatDate.getDate());
                  return chatDateOnly.getTime() === today.getTime();
                });

                const yesterdayChats = filteredChats.filter(chat => {
                  const chatDate = new Date(chat.createdAt);
                  const chatDateOnly = new Date(chatDate.getFullYear(), chatDate.getMonth(), chatDate.getDate());
                  return chatDateOnly.getTime() === yesterday.getTime();
                });

                const last30DaysChats = filteredChats.filter(chat => {
                  const chatDate = new Date(chat.createdAt);
                  const chatDateOnly = new Date(chatDate.getFullYear(), chatDate.getMonth(), chatDate.getDate());
                  return chatDateOnly < yesterday && chatDateOnly >= thirtyDaysAgo;
                });

                const olderChats = filteredChats.filter(chat => {
                  const chatDate = new Date(chat.createdAt);
                  const chatDateOnly = new Date(chatDate.getFullYear(), chatDate.getMonth(), chatDate.getDate());
                  return chatDateOnly < thirtyDaysAgo;
                });

                const sections = [
                  { title: "Today", chats: todayChats },
                  { title: "Yesterday", chats: yesterdayChats },
                  { title: "Last 30 Days", chats: last30DaysChats },
                  ...(olderChats.length > 0 ? [{ title: "Older", chats: olderChats }] : [])
                ];

                // Show appropriate message based on state
                if (!selectedApp) {
                  return (
                    <div className="px-3 py-8 text-center text-xs text-[#666]">
                      <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p>Select a project to view conversations</p>
                    </div>
                  );
                }

                // Show "No results" if search query exists but no chats match
                if (searchQuery.trim() && filteredChats.length === 0) {
                  return (
                    <div className="px-3 py-8 text-center text-xs text-[#666]">
                      No conversations found for "{searchQuery}"
                    </div>
                  );
                }

                // If no chats in any section, show all chats in a single list as fallback
                const totalChatsInSections = sections.reduce((total, section) => total + section.chats.length, 0);
                if (totalChatsInSections === 0 && filteredChats.length > 0) {
                  return (
                    <div>
                      <h3 className="text-xs font-medium text-[#666] mb-3">All Conversations</h3>
                      <div className="space-y-1">
                        {filteredChats.map(chat => (
                          <div
                            key={chat.id}
                            className={`group p-3 rounded-lg cursor-pointer transition-colors hover:bg-[#1a1a1a] ${
                              selectedChat?.id === chat.id ? 'bg-[#1a1a1a] border border-blue-500/30' : ''
                            }`}
                            onClick={() => {
                              selectChat(chat);
                              setIsLeftPanelOpen(false);
                              setCurrentView("main");
                            }}
                          >
                            <div className="flex items-start justify-between gap-2">
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                  <h4 className="text-xs font-medium text-white truncate">
                                    {chat.title || `Chat ${chat.id}`}
                                  </h4>
                                  <div className="bg-[#2a2a2a] text-[#888] text-[10px] px-1 py-0 rounded">
                                    {chat.messages?.length || 0}
                                  </div>
                                </div>
                                <p className="text-[10px] text-[#666] line-clamp-2 leading-relaxed">
                                  {chat.messages && chat.messages.length > 0
                                    ? (chat.messages[chat.messages.length - 1].content.length > 50
                                        ? chat.messages[chat.messages.length - 1].content.substring(0, 50) + '...'
                                        : chat.messages[chat.messages.length - 1].content)
                                    : 'No messages'
                                  }
                                </p>
                                <div className="flex items-center gap-1 mt-2">
                                  <span className="text-[10px] text-[#555]">
                                    {new Date(chat.updatedAt || chat.createdAt).toLocaleDateString()}
                                  </span>
                                </div>
                              </div>
                              <button
                                onClick={async (e) => {
                                  e.stopPropagation();
                                  try {
                                    await deleteDyadChat(chat.id);
                                  } catch (err) {
                                    console.error('Failed to delete chat:', err);
                                  }
                                }}
                                className="opacity-0 group-hover:opacity-100 h-6 w-6 p-0 text-red-400 hover:text-red-300 hover:bg-red-900/20 rounded flex items-center justify-center transition-all"
                              >
                                <Trash2 className="w-3 h-3" />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                }

                return sections.map(section => (
                  <div key={section.title}>
                    <h3 className="text-xs font-medium text-[#666] mb-3">{section.title}</h3>
                    <div className="space-y-1">
                      {section.chats.length > 0 ? (
                        section.chats.map(chat => (
                          <div
                            key={chat.id}
                            className={`group p-3 rounded-lg cursor-pointer transition-colors hover:bg-[#1a1a1a] ${
                              selectedChat?.id === chat.id ? 'bg-[#1a1a1a] border border-blue-500/30' : ''
                            }`}
                            onClick={() => {
                              selectChat(chat);
                              setIsLeftPanelOpen(false);
                              setCurrentView("main");
                            }}
                          >
                            <div className="flex items-start justify-between gap-2">
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                  <h4 className="text-xs font-medium text-white truncate">
                                    {chat.title || `Chat ${chat.id}`}
                                  </h4>
                                  <div className="bg-[#2a2a2a] text-[#888] text-[10px] px-1 py-0 rounded">
                                    {chat.messages?.length || 0}
                                  </div>
                                </div>
                                <p className="text-[10px] text-[#666] line-clamp-2 leading-relaxed">
                                  {chat.messages && chat.messages.length > 0
                                    ? (chat.messages[chat.messages.length - 1].content.length > 50
                                        ? chat.messages[chat.messages.length - 1].content.substring(0, 50) + '...'
                                        : chat.messages[chat.messages.length - 1].content)
                                    : 'No messages'
                                  }
                                </p>
                              </div>
                              <button
                                onClick={async (e) => {
                                  e.stopPropagation();
                                  try {
                                    await deleteDyadChat(chat.id);
                                  } catch (err) {
                                    console.error('Failed to delete chat:', err);
                                  }
                                }}
                                className="opacity-0 group-hover:opacity-100 h-6 w-6 p-0 text-red-400 hover:text-red-300 hover:bg-red-900/20 rounded flex items-center justify-center transition-all"
                              >
                                <Trash2 className="w-3 h-3" />
                              </button>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="px-3 py-2 text-xs text-[#666]">
                          No conversations {section.title.toLowerCase()}
                        </div>
                      )}
                    </div>
                  </div>
                ));
              })()}
            </div>
          </div>

          {/* Panel Footer */}
          <div className="border-t border-[#1a1a1a] p-4 space-y-2">
            <button className="w-full flex items-center gap-2 px-3 py-2 text-sm text-[#666] hover:text-white hover:bg-[#1a1a1a] rounded-lg transition-colors">
              <Settings className="w-4 h-4" />
              Settings
            </button>
            <button className="w-full flex items-center gap-2 px-3 py-2 text-sm text-[#666] hover:text-white hover:bg-[#1a1a1a] rounded-lg transition-colors">
              <HelpCircle className="w-4 h-4" />
              Help Center
            </button>
            <button className="w-full flex items-center gap-2 px-3 py-2 text-sm text-[#666] hover:text-white hover:bg-[#1a1a1a] rounded-lg transition-colors">
              <User className="w-4 h-4" />
              My Account
            </button>
          </div>
        </div>





        {/* Top Navigation Bar */}
        <header className="h-14 bg-[#0a0a0a] border-b border-[#1a1a1a] flex items-center justify-between px-6">
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-3">
              <div className="w-14 h-14 rounded-lg overflow-hidden flex items-center justify-center">
                <img
                  src="/logo.png"
                  alt="AI Dev Ecosystem"
                  className="w-full h-full object-contain"
                />
              </div>
              
            </div>
            <div className="flex items-center gap-1 text-xs text-[#666]">
              <span>Personal</span>
              <span>/</span>
              <span>The Orb</span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-3 text-xs text-[#888] hover:text-white hover:bg-[#1a1a1a] rounded-md"
              onClick={() => setCurrentView("dyad")}
            >
              <Settings className="w-3 h-3 mr-2" />
              Dyad
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-3 text-xs text-[#888] hover:text-white hover:bg-[#1a1a1a] rounded-md"
              onClick={() => setCurrentView("admin")}
            >
              <Settings className="w-3 h-3 mr-2" />
              Settings
            </Button>
            <Button 
              size="sm" 
              className="h-8 px-4 bg-white text-black hover:bg-gray-100 text-xs font-medium rounded-md"
            >
              Publish
            </Button>
            <div className="w-7 h-7 bg-[#1a1a1a] rounded-full flex items-center justify-center ml-2">
              <User className="w-4 h-4 text-[#888]" />
            </div>
          </div>
        </header>

        <div className="flex flex-1 overflow-hidden p-4 gap-4">
          {/* Left Sidebar - Chat */}
          <aside
            style={{ width: sidebarWidth }}
            className="bg-[#0a0a0a] rounded-xl flex flex-col overflow-hidden relative h-full shadow-xl"
          >
            {/* Chat Header */}
            <div className="px-6 py-4 border-b border-[#1a1a1a] flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <h2 className="font-medium text-white text-sm">Chat</h2>
                {selectedApp && (
                  <div className="text-xs text-[#666]">
                    • {selectedApp.name}
                  </div>
                )}
              </div>
              <div className="flex items-center gap-2">
                <Select value={selectedModel} onValueChange={setSelectedModel}>
                  <SelectTrigger className="w-48 h-7 bg-[#1a1a1a] border-[#333] text-xs text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-[#1a1a1a] border-[#333] text-white">
                    <SelectItem
                      value="moonshotai/kimi-k2"
                      className="text-xs text-white hover:bg-[#2a2a2a] focus:bg-[#2a2a2a] cursor-pointer"
                    >
                      Kimi K2
                    </SelectItem>
                    <SelectItem
                      value="x-ai/grok-4"
                      className="text-xs text-white hover:bg-[#2a2a2a] focus:bg-[#2a2a2a] cursor-pointer"
                    >
                      Grok 4
                    </SelectItem>
                    <SelectItem
                      value="anthropic/claude-sonnet-4"
                      className="text-xs text-white hover:bg-[#2a2a2a] focus:bg-[#2a2a2a] cursor-pointer"
                    >
                      Claude Sonnet 4
                    </SelectItem>
                    <SelectItem
                      value="anthropic/claude-3.7-sonnet:thinking"
                      className="text-xs text-white hover:bg-[#2a2a2a] focus:bg-[#2a2a2a] cursor-pointer"
                    >
                      Claude 3.7 Sonnet (Thinking)
                    </SelectItem>
                    <SelectItem
                      value="anthropic/claude-3.7-sonnet"
                      className="text-xs text-white hover:bg-[#2a2a2a] focus:bg-[#2a2a2a] cursor-pointer"
                    >
                      Claude 3.7 Sonnet
                    </SelectItem>
                    <SelectItem
                      value="openai/gpt-4.1"
                      className="text-xs text-white hover:bg-[#2a2a2a] focus:bg-[#2a2a2a] cursor-pointer"
                    >
                      GPT-4.1
                    </SelectItem>
                  </SelectContent>
                </Select>
                <button className="w-6 h-6 hover:bg-[#1a1a1a] rounded-md flex items-center justify-center transition-colors">
                  <MessageSquare className="w-4 h-4 text-[#666]" />
                </button>
              </div>
            </div>

            {/* Chat Messages */}
            <div className="flex-1 min-h-0 overflow-hidden">
              <ScrollArea className="h-full">
                <div
                  className="py-4 space-y-4"
                  style={{
                    paddingLeft: sidebarWidth > 400 ? '1.5rem' : '1rem',
                    paddingRight: sidebarWidth > 400 ? '1.5rem' : '1rem'
                  }}
                >
              {messages.map((message) => (
                <div key={message.id} className="group">
                  <div className={`flex gap-3 ${message.type === "user" ? "flex-row-reverse" : ""}`}>
                    <div className="flex-shrink-0">
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                          message.type === "user"
                            ? "bg-blue-600 text-white"
                            : "bg-[#1a1a1a] text-[#888] border border-[#2a2a2a]"
                        }`}
                      >
                        {message.type === "user" ? "U" : "AI"}
                      </div>
                    </div>
                    <div className={`flex-1 ${message.type === "user" ? "text-right" : ""}`}>
                      <div
                        className={`inline-block px-4 py-3 rounded-lg text-sm leading-relaxed max-w-[calc(100%-2rem)] ${
                          message.type === "user"
                            ? "bg-blue-600 text-white shadow-lg"
                            : "bg-[#111111] text-[#e5e5e5] shadow-md"
                        }`}
                        style={{
                          maxWidth: sidebarWidth > 500 ? 'calc(85% - 2rem)' : 'calc(90% - 2rem)',
                          wordBreak: 'break-word'
                        }}
                      >
                        <div className="whitespace-pre-wrap break-words">{message.content}</div>
                      </div>
                      <div className={`text-xs text-[#666] mt-1 ${message.type === "user" ? "text-right" : ""}`}>
                        {formatTime(message.timestamp)}
                      </div>
                    </div>
                  </div>

                  {/* Task List for AI messages */}
                  {message.type === "ai" && message.tasks && (
                    <div className="ml-10">
                      <TaskList tasks={message.tasks} title="Current Tasks" />
                    </div>
                  )}
                </div>
              ))}
                </div>
              </ScrollArea>
            </div>




            {/* Chat Input */}
            <div
              className="shrink-0 bg-[#0a0a0a] shadow-inner"
              style={{
                padding: sidebarWidth > 400 ? '0.75rem' : '0.5rem'
              }}
            >
              <div className="relative bg-[#111111] rounded-xl p-1 focus-within:shadow-blue-500/20 focus-within:shadow-lg transition-all shadow-md">
                <textarea
                  value={chatInput}
                  onChange={(e) => {
                    setChatInput(e.target.value);
                    // Auto-resize textarea
                    const textarea = e.target as HTMLTextAreaElement;
                    textarea.style.height = 'auto';
                    const scrollHeight = textarea.scrollHeight;
                    const maxHeight = 240; // 10 lines * 24px line height
                    textarea.style.height = Math.min(scrollHeight, maxHeight) + 'px';
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
                      e.preventDefault();
                      handleSendMessageClick();
                    }
                  }}
                  placeholder="Message AI..."
                  className="w-full bg-transparent px-4 py-3 pr-12 text-sm text-white placeholder:text-[#666] resize-none focus:outline-none leading-6 scrollbar-none overflow-y-auto"
                  rows={3}
                  style={{
                    minHeight: '84px',
                    maxHeight: '240px'
                  }}
                />
                <button
                  onClick={handleSendMessageClick}
                  disabled={!chatInput.trim() || isStreaming}
                  className="absolute right-2 top-3 w-8 h-8 bg-blue-600 hover:bg-blue-700 disabled:bg-[#333] disabled:text-[#666] rounded-lg flex items-center justify-center transition-colors shadow-md"
                >
                  {isStreaming ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <ArrowUp className="w-4 h-4" />
                  )}
                </button>
              </div>
            </div>

            {/* Resize Handle */}
            <div
              ref={resizeRef}
              className="absolute top-0 right-0 w-1 h-full cursor-col-resize hover:bg-blue-500 transition-colors rounded-r-xl"
              onMouseDown={handleMouseDown}
            />
          </aside>

          {/* Main Editor Area */}
          <main className="flex-1 flex flex-col bg-[#0a0a0a] rounded-xl overflow-hidden shadow-xl">
            {/* Editor Tabs */}
            <div className="h-12 bg-[#0a0a0a] border-b border-[#1a1a1a] flex items-center px-6">
              <div className="flex items-center gap-6">
                <button
                  className={`flex items-center gap-2 px-3 py-1.5 text-xs transition-colors ${
                    activeTab === "preview"
                      ? "text-white"
                      : "text-[#666] hover:text-white"
                  }`}
                  onClick={() => setActiveTab("preview")}
                >
                  <span className="text-sm">📱</span>
                  Preview
                  {activeTab === "preview" && (
                    <div className="w-2 h-2 rounded-full bg-green-500 ml-1" />
                  )}
                </button>
                <button
                  className={`flex items-center gap-2 px-3 py-1.5 text-xs transition-colors ${
                    activeTab === "code"
                      ? "text-white"
                      : "text-[#666] hover:text-white"
                  }`}
                  onClick={() => setActiveTab("code")}
                >
                  <span className="text-sm">📁</span>
                  Code
                  {activeTab === "code" && (
                    <div className="w-2 h-2 rounded-full bg-green-500 ml-1" />
                  )}
                </button>
                <button
                  className={`flex items-center gap-2 px-3 py-1.5 text-xs transition-colors ${
                    activeTab === "planning"
                      ? "text-white"
                      : "text-[#666] hover:text-white"
                  }`}
                  onClick={() => setActiveTab("planning")}
                >
                  <span className="text-sm">📋</span>
                  Planning
                  {activeTab === "planning" && (
                    <div className="w-2 h-2 rounded-full bg-green-500 ml-1" />
                  )}
                </button>
                <button
                  className={`flex items-center gap-2 px-3 py-1.5 text-xs transition-colors ${
                    activeTab === "graph"
                      ? "text-white"
                      : "text-[#666] hover:text-white"
                  }`}
                  onClick={() => setActiveTab("graph")}
                >
                  <span className="text-sm">❄️</span>
                  Graph
                  {activeTab === "graph" && (
                    <div className="w-2 h-2 rounded-full bg-green-500 ml-1" />
                  )}
                </button>
              </div>
            </div>

            {/* Content Area */}
            <div className="flex-1 bg-[#0a0a0a]">
              {activeTab === "preview" && (
                <DyadAppPreview selectedApp={selectedApp} />
              )}

              {activeTab === "code" && (
                <div className="h-full flex">
                  <DyadFileBrowser
                    selectedApp={selectedApp}
                    onFileSelect={setSelectedFile}
                    selectedFile={selectedFile}
                  />
                  <DyadCodeEditor
                    selectedApp={selectedApp}
                    selectedFile={selectedFile}
                  />
                </div>
              )}

              {activeTab === "planning" && (
                <DyadPlanning selectedApp={selectedApp} />
              )}

              {activeTab === "graph" && (
                <DyadGraph selectedApp={selectedApp} />
              )}
            </div>
          </main>
        </div>
      </div>
  )
}
