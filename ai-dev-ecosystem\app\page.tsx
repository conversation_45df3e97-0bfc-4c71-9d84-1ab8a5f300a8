"use client"

import React from "react"
import { useState, useRef, use<PERSON><PERSON>back, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  MessageSquare,
  Settings,
  Upload,
  Eye,
  Code,
  ChevronLeft,
  ChevronRight,
  RotateCcw,
  Search,
  ArrowUp,
  User,
  Bot,
  Loader2,
  FileText,
  GitBranch,
  BarChart3,
} from "lucide-react"
import AdminDashboard from "@/components/admin-dashboard"
import TaskList from "@/components/task-list"
import StreamingPreview from "@/components/streaming-preview"
import ConversationThreads from "@/components/conversation-threads"
import DyadIntegration from "@/components/dyad-integration"
import ConversationSidebar from "@/components/conversation-sidebar"
import DyadFileBrowser from "@/components/dyad-file-browser"
import DyadCodeEditor from "@/components/dyad-code-editor"
import DyadAppPreview from "@/components/dyad-app-preview"
import DyadPlanning from "@/components/dyad-planning"
import DyadGraph from "@/components/dyad-graph"
import { useIntegratedChat } from "@/hooks/use-integrated-chat"
import { ScrollArea } from "@/components/ui/scroll-area"

interface Message {
  id: string
  type: "user" | "ai"
  content: string
  timestamp: Date
  tasks?: Task[]
}

interface Task {
  id: string
  title: string
  status: "pending" | "running" | "completed" | "error"
  type: "component" | "api" | "database" | "test"
  progress?: number
}

export default function AIDevInterface() {
  const [currentView, setCurrentView] = useState<"main" | "admin" | "dyad">("main")
  const [activeTab, setActiveTab] = useState<"preview" | "code" | "planning" | "graph">("preview")
  const [chatInput, setChatInput] = useState("")
  const [selectedModel, setSelectedModel] = useState("anthropic/claude-sonnet-4")
  const [sidebarWidth, setSidebarWidth] = useState(400) // Default width for SSR
  const [isResizing, setIsResizing] = useState(false)
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)
  const [selectedFile, setSelectedFile] = useState<string>()

  // Use integrated chat hook
  const {
    selectedApp,
    selectedChat,
    messages,
    isStreaming,
    handleSendMessage,
    handleCreateChat,
    handleSelectChat,
    formatTime,
    models,
  } = useIntegratedChat()

  // Set proper sidebar width after hydration
  useEffect(() => {
    if (typeof window !== "undefined") {
      const calculatedWidth = Math.floor((window.innerWidth - 48) / 3)
      setSidebarWidth(calculatedWidth)
    }
  }, [])

  const resizeRef = useRef<HTMLDivElement>(null)
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Handle sidebar hover
  const handleMouseEnterLeft = useCallback(() => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current)
    }
    hoverTimeoutRef.current = setTimeout(() => {
      setIsSidebarOpen(true)
    }, 300) // 300ms delay before opening
  }, [])

  const handleMouseLeaveLeft = useCallback(() => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current)
    }
    hoverTimeoutRef.current = setTimeout(() => {
      setIsSidebarOpen(false)
    }, 500) // 500ms delay before closing
  }, [])

  const handleSendMessageClick = async () => {
    if (!chatInput.trim()) return

    const message = chatInput
    setChatInput("")

    // Use the integrated chat handler
    await handleSendMessage(message, selectedModel)
  }

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsResizing(true)
    e.preventDefault()
  }, [])

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isResizing) return
      const newWidth = e.clientX - 16 // Account for padding
      setSidebarWidth(Math.max(300, Math.min(800, newWidth)))
    },
    [isResizing]
  )

  const handleMouseUp = useCallback(() => {
    setIsResizing(false)
  }, [])

  useEffect(() => {
    if (isResizing) {
      document.addEventListener("mousemove", handleMouseMove)
      document.addEventListener("mouseup", handleMouseUp)
      return () => {
        document.removeEventListener("mousemove", handleMouseMove)
        document.removeEventListener("mouseup", handleMouseUp)
      }
    }
  }, [isResizing, handleMouseMove, handleMouseUp])

  if (currentView === "admin") {
    return <AdminDashboard onBack={() => setCurrentView("main")} />
  }

  if (currentView === "dyad") {
    return (
      <div className="h-screen w-full bg-[#000000] text-[#e5e5e5] font-sans text-sm flex flex-col overflow-hidden">
        {/* Top Navigation Bar */}
        <header className="h-14 bg-[#0a0a0a] border-b border-[#1a1a1a] flex items-center justify-between px-6">
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-3">
              <div className="w-14 h-14 rounded-lg overflow-hidden flex items-center justify-center">
                <img
                  src="/logo.png"
                  alt="AI Dev Ecosystem"
                  className="w-full h-full object-contain"
                />
              </div>
            </div>
            <div className="flex items-center gap-1 text-xs text-[#666]">
              <span>Dyad Integration</span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-3 text-xs text-[#888] hover:text-white hover:bg-[#1a1a1a] rounded-md"
              onClick={() => setCurrentView("main")}
            >
              Back to Main
            </Button>
            <div className="w-7 h-7 bg-[#1a1a1a] rounded-full flex items-center justify-center ml-2">
              <User className="w-4 h-4 text-[#888]" />
            </div>
          </div>
        </header>

        {/* Dyad Integration Content */}
        <div className="flex-1 overflow-hidden p-6">
          <DyadIntegration />
        </div>
      </div>
    )
  }

  return (
    <div className="h-screen w-full bg-[#000000] text-[#e5e5e5] font-sans text-sm flex flex-col overflow-hidden relative">
        <ConversationThreads isVisible={false} onSelectThread={() => {}} />

        {/* Left edge hover area */}
        <div
          className="absolute left-0 top-0 w-4 h-full z-30"
          onMouseEnter={handleMouseEnterLeft}
          onMouseLeave={handleMouseLeaveLeft}
        />

        {/* Conversation Sidebar */}
        <ConversationSidebar
          isOpen={isSidebarOpen}
          onSelectChat={handleSelectChat}
          selectedChatId={selectedChat?.id}
          selectedApp={selectedApp}
          onCreateChat={handleCreateChat}
        />



        {/* Top Navigation Bar */}
        <header className="h-14 bg-[#0a0a0a] border-b border-[#1a1a1a] flex items-center justify-between px-6">
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-3">
              <div className="w-14 h-14 rounded-lg overflow-hidden flex items-center justify-center">
                <img
                  src="/logo.png"
                  alt="AI Dev Ecosystem"
                  className="w-full h-full object-contain"
                />
              </div>
              
            </div>
            <div className="flex items-center gap-1 text-xs text-[#666]">
              <span>Personal</span>
              <span>/</span>
              <span>The Orb</span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-3 text-xs text-[#888] hover:text-white hover:bg-[#1a1a1a] rounded-md"
              onClick={() => setCurrentView("dyad")}
            >
              <Settings className="w-3 h-3 mr-2" />
              Dyad
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-3 text-xs text-[#888] hover:text-white hover:bg-[#1a1a1a] rounded-md"
              onClick={() => setCurrentView("admin")}
            >
              <Settings className="w-3 h-3 mr-2" />
              Settings
            </Button>
            <Button 
              size="sm" 
              className="h-8 px-4 bg-white text-black hover:bg-gray-100 text-xs font-medium rounded-md"
            >
              Publish
            </Button>
            <div className="w-7 h-7 bg-[#1a1a1a] rounded-full flex items-center justify-center ml-2">
              <User className="w-4 h-4 text-[#888]" />
            </div>
          </div>
        </header>

        <div className="flex flex-1 overflow-hidden p-4 gap-4">
          {/* Left Sidebar - Chat */}
          <aside
            style={{ width: sidebarWidth }}
            className="bg-[#0a0a0a] rounded-xl flex flex-col overflow-hidden relative h-full shadow-xl"
          >
            {/* Chat Header */}
            <div className="px-6 py-4 border-b border-[#1a1a1a] flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <h2 className="font-medium text-white text-sm">Chat</h2>
                {selectedApp && (
                  <div className="text-xs text-[#666]">
                    • {selectedApp.name}
                  </div>
                )}
              </div>
              <div className="flex items-center gap-2">
                <Select value={selectedModel} onValueChange={setSelectedModel}>
                  <SelectTrigger className="w-48 h-7 bg-[#1a1a1a] border-[#333] text-xs text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-[#1a1a1a] border-[#333] text-white">
                    <SelectItem
                      value="moonshotai/kimi-k2"
                      className="text-xs text-white hover:bg-[#2a2a2a] focus:bg-[#2a2a2a] cursor-pointer"
                    >
                      Kimi K2
                    </SelectItem>
                    <SelectItem
                      value="x-ai/grok-4"
                      className="text-xs text-white hover:bg-[#2a2a2a] focus:bg-[#2a2a2a] cursor-pointer"
                    >
                      Grok 4
                    </SelectItem>
                    <SelectItem
                      value="anthropic/claude-sonnet-4"
                      className="text-xs text-white hover:bg-[#2a2a2a] focus:bg-[#2a2a2a] cursor-pointer"
                    >
                      Claude Sonnet 4
                    </SelectItem>
                    <SelectItem
                      value="anthropic/claude-3.7-sonnet:thinking"
                      className="text-xs text-white hover:bg-[#2a2a2a] focus:bg-[#2a2a2a] cursor-pointer"
                    >
                      Claude 3.7 Sonnet (Thinking)
                    </SelectItem>
                    <SelectItem
                      value="anthropic/claude-3.7-sonnet"
                      className="text-xs text-white hover:bg-[#2a2a2a] focus:bg-[#2a2a2a] cursor-pointer"
                    >
                      Claude 3.7 Sonnet
                    </SelectItem>
                    <SelectItem
                      value="openai/gpt-4.1"
                      className="text-xs text-white hover:bg-[#2a2a2a] focus:bg-[#2a2a2a] cursor-pointer"
                    >
                      GPT-4.1
                    </SelectItem>
                  </SelectContent>
                </Select>
                <button className="w-6 h-6 hover:bg-[#1a1a1a] rounded-md flex items-center justify-center transition-colors">
                  <MessageSquare className="w-4 h-4 text-[#666]" />
                </button>
              </div>
            </div>

            {/* Chat Messages */}
            <div className="flex-1 min-h-0 overflow-hidden">
              <ScrollArea className="h-full">
                <div className="px-6 py-4 space-y-4">
              {messages.map((message) => (
                <div key={message.id} className="group">
                  <div className={`flex gap-3 ${message.type === "user" ? "flex-row-reverse" : ""}`}>
                    <div className="flex-shrink-0">
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                          message.type === "user"
                            ? "bg-blue-600 text-white"
                            : "bg-[#1a1a1a] text-[#888] border border-[#2a2a2a]"
                        }`}
                      >
                        {message.type === "user" ? "U" : "AI"}
                      </div>
                    </div>
                    <div className={`flex-1 max-w-[85%] ${message.type === "user" ? "text-right" : ""}`}>
                      <div
                        className={`inline-block px-4 py-3 rounded-lg text-sm leading-relaxed ${
                          message.type === "user"
                            ? "bg-blue-600 text-white shadow-lg"
                            : "bg-[#111111] text-[#e5e5e5] shadow-md"
                        }`}
                      >
                        <div className="whitespace-pre-wrap">{message.content}</div>
                      </div>
                      <div className={`text-xs text-[#666] mt-1 ${message.type === "user" ? "text-right" : ""}`}>
                        {formatTime(message.timestamp)}
                      </div>
                    </div>
                  </div>

                  {/* Task List for AI messages */}
                  {message.type === "ai" && message.tasks && (
                    <div className="ml-10">
                      <TaskList tasks={message.tasks} title="Current Tasks" />
                    </div>
                  )}
                </div>
              ))}
                </div>
              </ScrollArea>
            </div>




            {/* Chat Input */}
            <div className="shrink-0 p-3 bg-[#0a0a0a] shadow-inner">
              <div className="relative bg-[#111111] rounded-xl p-1 focus-within:shadow-blue-500/20 focus-within:shadow-lg transition-all shadow-md">
                <textarea
                  value={chatInput}
                  onChange={(e) => {
                    setChatInput(e.target.value);
                    // Auto-resize textarea
                    const textarea = e.target as HTMLTextAreaElement;
                    textarea.style.height = 'auto';
                    const scrollHeight = textarea.scrollHeight;
                    const maxHeight = 240; // 10 lines * 24px line height
                    textarea.style.height = Math.min(scrollHeight, maxHeight) + 'px';
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
                      e.preventDefault();
                      handleSendMessageClick();
                    }
                  }}
                  placeholder="Message AI..."
                  className="w-full bg-transparent px-4 py-3 pr-12 text-sm text-white placeholder:text-[#666] resize-none focus:outline-none leading-6 scrollbar-none overflow-y-auto"
                  rows={3}
                  style={{
                    minHeight: '84px',
                    maxHeight: '240px'
                  }}
                />
                <button
                  onClick={handleSendMessageClick}
                  disabled={!chatInput.trim() || isStreaming}
                  className="absolute right-2 top-3 w-8 h-8 bg-blue-600 hover:bg-blue-700 disabled:bg-[#333] disabled:text-[#666] rounded-lg flex items-center justify-center transition-colors shadow-md"
                >
                  {isStreaming ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <ArrowUp className="w-4 h-4" />
                  )}
                </button>
              </div>
            </div>

            {/* Resize Handle */}
            <div
              ref={resizeRef}
              className="absolute top-0 right-0 w-1 h-full cursor-col-resize hover:bg-blue-500 transition-colors rounded-r-xl"
              onMouseDown={handleMouseDown}
            />
          </aside>

          {/* Main Editor Area */}
          <main className="flex-1 flex flex-col bg-[#0a0a0a] rounded-xl overflow-hidden shadow-xl">
            {/* Editor Tabs */}
            <div className="h-12 bg-[#0a0a0a] border-b border-[#1a1a1a] flex items-center px-6">
              <div className="flex items-center gap-6">
                <button
                  className={`flex items-center gap-2 px-3 py-1.5 text-xs transition-colors ${
                    activeTab === "preview"
                      ? "text-white"
                      : "text-[#666] hover:text-white"
                  }`}
                  onClick={() => setActiveTab("preview")}
                >
                  <span className="text-sm">📱</span>
                  Preview
                  {activeTab === "preview" && (
                    <div className="w-2 h-2 rounded-full bg-green-500 ml-1" />
                  )}
                </button>
                <button
                  className={`flex items-center gap-2 px-3 py-1.5 text-xs transition-colors ${
                    activeTab === "code"
                      ? "text-white"
                      : "text-[#666] hover:text-white"
                  }`}
                  onClick={() => setActiveTab("code")}
                >
                  <span className="text-sm">📁</span>
                  Code
                  {activeTab === "code" && (
                    <div className="w-2 h-2 rounded-full bg-green-500 ml-1" />
                  )}
                </button>
                <button
                  className={`flex items-center gap-2 px-3 py-1.5 text-xs transition-colors ${
                    activeTab === "planning"
                      ? "text-white"
                      : "text-[#666] hover:text-white"
                  }`}
                  onClick={() => setActiveTab("planning")}
                >
                  <span className="text-sm">📋</span>
                  Planning
                  {activeTab === "planning" && (
                    <div className="w-2 h-2 rounded-full bg-green-500 ml-1" />
                  )}
                </button>
                <button
                  className={`flex items-center gap-2 px-3 py-1.5 text-xs transition-colors ${
                    activeTab === "graph"
                      ? "text-white"
                      : "text-[#666] hover:text-white"
                  }`}
                  onClick={() => setActiveTab("graph")}
                >
                  <span className="text-sm">❄️</span>
                  Graph
                  {activeTab === "graph" && (
                    <div className="w-2 h-2 rounded-full bg-green-500 ml-1" />
                  )}
                </button>
              </div>
            </div>

            {/* Content Area */}
            <div className="flex-1 bg-[#0a0a0a]">
              {activeTab === "preview" && (
                <DyadAppPreview selectedApp={selectedApp} />
              )}

              {activeTab === "code" && (
                <div className="h-full flex">
                  <DyadFileBrowser
                    selectedApp={selectedApp}
                    onFileSelect={setSelectedFile}
                    selectedFile={selectedFile}
                  />
                  <DyadCodeEditor
                    selectedApp={selectedApp}
                    selectedFile={selectedFile}
                  />
                </div>
              )}

              {activeTab === "planning" && (
                <DyadPlanning selectedApp={selectedApp} />
              )}

              {activeTab === "graph" && (
                <DyadGraph selectedApp={selectedApp} />
              )}
            </div>
          </main>
        </div>
      </div>
  )
}
