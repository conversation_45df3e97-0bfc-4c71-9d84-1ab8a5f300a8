import { Router } from 'express';
import { Server } from 'socket.io';
import { z } from 'zod';
import log from 'electron-log';
import { db } from '../../db';
import { chats, chatMessages } from '../../db/schema';
import { eq, desc } from 'drizzle-orm';

const logger = log.scope('web-server:chats');

// Validation schemas
const CreateChatSchema = z.object({
  appId: z.number(),
  title: z.string().optional(),
});

const SendMessageSchema = z.object({
  content: z.string().min(1),
  role: z.enum(['user', 'assistant']).default('user'),
});

const StreamChatSchema = z.object({
  message: z.string().min(1),
  model: z.object({
    name: z.string(),
    provider: z.string(),
  }),
  appId: z.number(),
});

export function setupChatRoutes(io: Server) {
  const router = Router();

  // GET /api/chats - List chats (optionally filtered by appId)
  router.get('/', async (req, res) => {
    try {
      const appId = req.query.appId ? parseInt(req.query.appId as string) : null;
      
      let allChats;

      if (appId) {
        allChats = await db.select().from(chats)
          .where(eq(chats.appId, appId))
          .orderBy(desc(chats.createdAt));
      } else {
        allChats = await db.select().from(chats)
          .orderBy(desc(chats.createdAt));
      }
      res.json(allChats);
    } catch (error) {
      logger.error('Error listing chats:', error);
      res.status(500).json({ error: 'Failed to list chats' });
    }
  });

  // GET /api/chats/:id - Get specific chat with messages
  router.get('/:id', async (req, res) => {
    try {
      const chatId = parseInt(req.params.id);
      if (isNaN(chatId)) {
        return res.status(400).json({ error: 'Invalid chat ID' });
      }

      const chat = await db.select().from(chats)
        .where(eq(chats.id, chatId))
        .limit(1);

      if (chat.length === 0) {
        return res.status(404).json({ error: 'Chat not found' });
      }

      // Get messages for this chat
      const messages = await db.select().from(chatMessages)
        .where(eq(chatMessages.chatId, chatId))
        .orderBy(chatMessages.createdAt);

      const chatWithMessages = {
        ...chat[0],
        messages,
      };

      res.json(chatWithMessages);
    } catch (error) {
      logger.error('Error getting chat:', error);
      res.status(500).json({ error: 'Failed to get chat' });
    }
  });

  // POST /api/chats - Create new chat
  router.post('/', async (req, res) => {
    try {
      const params = CreateChatSchema.parse(req.body);
      
      const [chat] = await db
        .insert(chats)
        .values({
          appId: params.appId,
          title: params.title,
        })
        .returning();

      logger.info(`Created chat: ${chat.id} for app ${params.appId}`);
      res.status(201).json(chat);
    } catch (error) {
      logger.error('Error creating chat:', error);
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: 'Invalid request data', details: error.errors });
      } else {
        res.status(500).json({ error: 'Failed to create chat' });
      }
    }
  });

  // POST /api/chats/:id/messages - Add message to chat
  router.post('/:id/messages', async (req, res) => {
    try {
      const chatId = parseInt(req.params.id);
      if (isNaN(chatId)) {
        return res.status(400).json({ error: 'Invalid chat ID' });
      }

      const messageData = SendMessageSchema.parse(req.body);

      // Verify chat exists
      const chat = await db.select().from(chats)
        .where(eq(chats.id, chatId))
        .limit(1);

      if (chat.length === 0) {
        return res.status(404).json({ error: 'Chat not found' });
      }

      // Add message to chat
      const [message] = await db
        .insert(chatMessages)
        .values({
          chatId,
          content: messageData.content,
          role: messageData.role,
        })
        .returning();

      logger.info(`Added message to chat ${chatId}`);
      res.status(201).json(message);
    } catch (error) {
      logger.error('Error adding message:', error);
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: 'Invalid request data', details: error.errors });
      } else {
        res.status(500).json({ error: 'Failed to add message' });
      }
    }
  });

  // POST /api/chats/:id/stream - Stream chat response
  router.post('/:id/stream', async (req, res) => {
    try {
      const chatId = parseInt(req.params.id);
      if (isNaN(chatId)) {
        return res.status(400).json({ error: 'Invalid chat ID' });
      }

      const streamData = StreamChatSchema.parse(req.body);

      // Verify chat exists
      const chat = await db.select().from(chats)
        .where(eq(chats.id, chatId))
        .limit(1);

      if (chat.length === 0) {
        return res.status(404).json({ error: 'Chat not found' });
      }

      // Set up Server-Sent Events
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      });

      // Add user message to chat
      await db.insert(chatMessages).values({
        chatId,
        content: streamData.message,
        role: 'user',
      });

      // TODO: Implement actual LLM streaming here
      // For now, send a mock response
      const mockResponse = "This is a mock response from the chat API. The actual LLM integration would go here.";
      
      // Simulate streaming response
      const words = mockResponse.split(' ');
      for (let i = 0; i < words.length; i++) {
        const chunk = words[i] + (i < words.length - 1 ? ' ' : '');
        res.write(`data: ${JSON.stringify({ content: chunk, done: false })}\n\n`);
        await new Promise(resolve => setTimeout(resolve, 100)); // Simulate delay
      }

      // Add assistant message to chat
      await db.insert(chatMessages).values({
        chatId,
        content: mockResponse,
        role: 'assistant',
      });

      // Send completion signal
      res.write(`data: ${JSON.stringify({ content: '', done: true })}\n\n`);
      res.end();

      logger.info(`Streamed response for chat ${chatId}`);
    } catch (error) {
      logger.error('Error streaming chat:', error);
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: 'Invalid request data', details: error.errors });
      } else {
        res.status(500).json({ error: 'Failed to stream chat' });
      }
    }
  });

  // DELETE /api/chats/:id - Delete chat
  router.delete('/:id', async (req, res) => {
    try {
      const chatId = parseInt(req.params.id);
      if (isNaN(chatId)) {
        return res.status(400).json({ error: 'Invalid chat ID' });
      }

      // Delete chat messages first (due to foreign key constraint)
      await db.delete(chatMessages).where(eq(chatMessages.chatId, chatId));
      
      // Delete chat
      const result = await db.delete(chats).where(eq(chats.id, chatId));

      if (result.rowsAffected === 0) {
        return res.status(404).json({ error: 'Chat not found' });
      }

      logger.info(`Deleted chat: ${chatId}`);
      res.json({ message: 'Chat deleted successfully' });
    } catch (error) {
      logger.error('Error deleting chat:', error);
      res.status(500).json({ error: 'Failed to delete chat' });
    }
  });

  // PUT /api/chats/:id - Update chat (e.g., title)
  router.put('/:id', async (req, res) => {
    try {
      const chatId = parseInt(req.params.id);
      if (isNaN(chatId)) {
        return res.status(400).json({ error: 'Invalid chat ID' });
      }

      const { title } = req.body;

      if (typeof title !== 'string') {
        return res.status(400).json({ error: 'Title must be a string' });
      }

      const [updatedChat] = await db
        .update(chats)
        .set({ title })
        .where(eq(chats.id, chatId))
        .returning();

      if (!updatedChat) {
        return res.status(404).json({ error: 'Chat not found' });
      }

      res.json(updatedChat);
    } catch (error) {
      logger.error('Error updating chat:', error);
      res.status(500).json({ error: 'Failed to update chat' });
    }
  });

  return router;
}
