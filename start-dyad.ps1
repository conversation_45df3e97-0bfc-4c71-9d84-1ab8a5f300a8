Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Starting Dyad Frontend + Backend" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check current directory
Write-Host "Current directory: $PWD" -ForegroundColor Yellow
Write-Host ""

# Check if Node.js is installed
Write-Host "Checking Node.js..." -ForegroundColor Green
try {
    $nodeVersion = node --version
    Write-Host "Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Node.js is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if npm is installed  
Write-Host "Checking npm..." -ForegroundColor Green
try {
    $npmVersion = npm --version
    Write-Host "npm version: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: npm is not installed or not in PATH" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Check required files
if (-not (Test-Path "package.json")) {
    Write-Host "ERROR: package.json not found in current directory" -ForegroundColor Red
    Write-Host "Make sure you're running this script from the dyad-main folder" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

if (-not (Test-Path "ai-dev-ecosystem")) {
    Write-Host "ERROR: ai-dev-ecosystem folder not found" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

if (-not (Test-Path "ai-dev-ecosystem\package.json")) {
    Write-Host "ERROR: package.json not found in ai-dev-ecosystem folder" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "All required files found" -ForegroundColor Green
Write-Host ""

Write-Host "Starting Backend (Dyad Main App)..." -ForegroundColor Yellow
Write-Host "Backend will run in a new window" -ForegroundColor Yellow
Write-Host ""

# Start backend in new window
Start-Process powershell -ArgumentList "-NoExit", "-Command", "Write-Host 'Starting Dyad Backend...' -ForegroundColor Cyan; npm start"

Write-Host "Waiting 5 seconds for backend to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

Write-Host ""
Write-Host "Starting Frontend (AI Dev Ecosystem)..." -ForegroundColor Yellow  
Write-Host "Frontend will run in a new window" -ForegroundColor Yellow
Write-Host ""

# Start frontend in new window
Start-Process powershell -ArgumentList "-NoExit", "-Command", "Write-Host 'Starting Dyad Frontend...' -ForegroundColor Cyan; Set-Location ai-dev-ecosystem; npm run dev"

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Both services are starting up!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Backend (Dyad): Starting in separate window" -ForegroundColor Green
Write-Host "Frontend (Web UI): Starting in separate window (http://localhost:3001)" -ForegroundColor Green
Write-Host ""
Write-Host "Wait a few moments for both services to fully start." -ForegroundColor Yellow
Write-Host "The frontend will be available at: http://localhost:3001" -ForegroundColor Cyan
Write-Host ""
Write-Host "To stop the services:" -ForegroundColor Yellow
Write-Host "- Close the Backend PowerShell window" -ForegroundColor Yellow
Write-Host "- Close the Frontend PowerShell window" -ForegroundColor Yellow
Write-Host ""
Write-Host "This window will stay open for monitoring." -ForegroundColor Yellow
Read-Host "Press Enter to exit this launcher"
