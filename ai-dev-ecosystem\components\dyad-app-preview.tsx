"use client"

import React, { useState, useEffect } from 'react';
import { apiClient, App } from '@/lib/dyad-client';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Play, 
  Square, 
  RotateCcw, 
  ExternalLink, 
  Globe,
  AlertCircle,
  Loader2,
  Monitor,
  Smartphone,
  Tablet
} from 'lucide-react';

interface DyadAppPreviewProps {
  selectedApp?: App;
}

export default function DyadAppPreview({ selectedApp }: DyadAppPreviewProps) {
  const [isRunning, setIsRunning] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');

  // Check app status when app changes
  useEffect(() => {
    if (selectedApp) {
      checkAppStatus();
    } else {
      setIsRunning(false);
      setPreviewUrl(null);
      setError(null);
    }
  }, [selectedApp]);

  const checkAppStatus = async () => {
    if (!selectedApp) return;

    try {
      const status = await apiClient.getAppStatus(selectedApp.id);
      setIsRunning(status.isRunning);
      setPreviewUrl(status.url || null);
      setError(null);
    } catch (err) {
      console.error('Failed to check app status:', err);
      // For demo purposes, set mock data
      setIsRunning(false);
      setPreviewUrl(null);
    }
  };

  const startApp = async () => {
    if (!selectedApp) return;

    try {
      setIsLoading(true);
      setError(null);
      
      const result = await apiClient.startApp(selectedApp.id);
      setIsRunning(true);
      setPreviewUrl(result.url || `http://localhost:${3000 + selectedApp.id}`);
    } catch (err) {
      console.error('Failed to start app:', err);
      setError('Failed to start application');
      // For demo, simulate starting
      setTimeout(() => {
        setIsRunning(true);
        setPreviewUrl(`http://localhost:${3000 + selectedApp.id}`);
        setError(null);
      }, 2000);
    } finally {
      setIsLoading(false);
    }
  };

  const stopApp = async () => {
    if (!selectedApp) return;

    try {
      setIsLoading(true);
      await apiClient.stopApp(selectedApp.id);
      setIsRunning(false);
      setPreviewUrl(null);
      setError(null);
    } catch (err) {
      console.error('Failed to stop app:', err);
      setError('Failed to stop application');
      // For demo, simulate stopping
      setTimeout(() => {
        setIsRunning(false);
        setPreviewUrl(null);
        setError(null);
      }, 1000);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshPreview = () => {
    if (previewUrl) {
      // Force iframe reload
      const iframe = document.getElementById('app-preview') as HTMLIFrameElement;
      if (iframe) {
        iframe.src = iframe.src;
      }
    }
  };

  const openInNewTab = () => {
    if (previewUrl) {
      window.open(previewUrl, '_blank');
    }
  };

  const getViewModeStyles = () => {
    switch (viewMode) {
      case 'mobile':
        return { width: '375px', height: '667px' };
      case 'tablet':
        return { width: '768px', height: '1024px' };
      default:
        return { width: '100%', height: '100%' };
    }
  };

  const getViewModeIcon = (mode: 'desktop' | 'tablet' | 'mobile') => {
    switch (mode) {
      case 'mobile':
        return <Smartphone className="w-3 h-3" />;
      case 'tablet':
        return <Tablet className="w-3 h-3" />;
      default:
        return <Monitor className="w-3 h-3" />;
    }
  };

  if (!selectedApp) {
    return (
      <div className="flex-1 bg-[#0a0a0a] flex items-center justify-center">
        <div className="text-center text-[#666] p-8">
          <Globe className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-medium mb-2">No app selected</h3>
          <p className="text-sm">Select an app to preview it</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 bg-[#0a0a0a] flex flex-col">
      {/* Preview Header */}
      <div className="h-12 bg-[#111111] border-b border-[#1a1a1a] flex items-center justify-between px-4">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <Globe className="w-4 h-4 text-green-500" />
            <span className="text-sm text-white font-medium">{selectedApp.name}</span>
            <Badge 
              variant="secondary" 
              className={`text-[10px] px-2 py-0 ${
                isRunning 
                  ? 'bg-green-900/20 text-green-400 border-green-800' 
                  : 'bg-gray-900/20 text-gray-400 border-gray-800'
              }`}
            >
              {isRunning ? 'Running' : 'Stopped'}
            </Badge>
          </div>
          
          {previewUrl && (
            <span className="text-xs text-[#666]">{previewUrl}</span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {/* View Mode Selector */}
          <div className="flex items-center gap-1 mr-2">
            {(['desktop', 'tablet', 'mobile'] as const).map((mode) => (
              <Button
                key={mode}
                size="sm"
                variant="ghost"
                onClick={() => setViewMode(mode)}
                className={`h-7 w-7 p-0 ${
                  viewMode === mode 
                    ? 'bg-[#1a1a1a] text-white' 
                    : 'text-[#666] hover:text-white hover:bg-[#1a1a1a]'
                }`}
              >
                {getViewModeIcon(mode)}
              </Button>
            ))}
          </div>
          
          {previewUrl && (
            <>
              <Button
                size="sm"
                variant="ghost"
                onClick={refreshPreview}
                className="h-7 px-2 text-[#888] hover:text-white hover:bg-[#1a1a1a]"
              >
                <RotateCcw className="w-3 h-3" />
              </Button>
              
              <Button
                size="sm"
                variant="ghost"
                onClick={openInNewTab}
                className="h-7 px-2 text-[#888] hover:text-white hover:bg-[#1a1a1a]"
              >
                <ExternalLink className="w-3 h-3" />
              </Button>
            </>
          )}
          
          {isRunning ? (
            <Button
              size="sm"
              onClick={stopApp}
              disabled={isLoading}
              className="h-7 px-3 bg-red-600 hover:bg-red-700 disabled:bg-[#333] disabled:text-[#666]"
            >
              {isLoading ? (
                <Loader2 className="w-3 h-3 mr-1 animate-spin" />
              ) : (
                <Square className="w-3 h-3 mr-1" />
              )}
              Stop
            </Button>
          ) : (
            <Button
              size="sm"
              onClick={startApp}
              disabled={isLoading}
              className="h-7 px-3 bg-green-600 hover:bg-green-700 disabled:bg-[#333] disabled:text-[#666]"
            >
              {isLoading ? (
                <Loader2 className="w-3 h-3 mr-1 animate-spin" />
              ) : (
                <Play className="w-3 h-3 mr-1" />
              )}
              Start
            </Button>
          )}
        </div>
      </div>

      {/* Preview Content */}
      <div className="flex-1 relative bg-[#0a0a0a] overflow-hidden">
        {error ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-red-400 p-8">
              <AlertCircle className="w-8 h-8 mx-auto mb-2" />
              <p className="text-sm mb-4">{error}</p>
              <Button
                size="sm"
                variant="outline"
                onClick={checkAppStatus}
                className="bg-[#1a1a1a] border-[#333] text-white hover:bg-[#2a2a2a]"
              >
                Retry
              </Button>
            </div>
          </div>
        ) : isLoading ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-[#666]">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
              <p className="text-sm">
                {isRunning ? 'Stopping application...' : 'Starting application...'}
              </p>
            </div>
          </div>
        ) : !isRunning ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-[#666] p-8">
              <Play className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">Application not running</h3>
              <p className="text-sm mb-4">Start the application to see the preview</p>
              <Button
                onClick={startApp}
                className="bg-green-600 hover:bg-green-700"
              >
                <Play className="w-4 h-4 mr-2" />
                Start Application
              </Button>
            </div>
          </div>
        ) : previewUrl ? (
          <>
            {viewMode === 'desktop' ? (
              <div className="w-full h-full">
                <iframe
                  id="app-preview"
                  src={previewUrl}
                  className="w-full h-full border-none bg-white"
                  title={`${selectedApp.name} Preview`}
                  sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
                />
              </div>
            ) : (
              <div className="h-full flex items-center justify-center bg-[#111111]">
                <div
                  className="bg-white rounded-lg shadow-2xl overflow-hidden transition-all duration-300"
                  style={getViewModeStyles()}
                >
                  <iframe
                    id="app-preview"
                    src={previewUrl}
                    className="w-full h-full border-none"
                    title={`${selectedApp.name} Preview`}
                    sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
                  />
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-[#666] p-8">
              <AlertCircle className="w-8 h-8 mx-auto mb-2" />
              <p className="text-sm">Preview URL not available</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
