import { useState, useEffect, useCallback } from 'react';
import { apiClient, App, Cha<PERSON>, ChatMessage, LanguageModel } from '@/lib/dyad-client';

export function useDyad() {
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Test connection on mount
  useEffect(() => {
    const testConnection = async () => {
      try {
        await apiClient.healthCheck();
        setIsConnected(true);
        setError(null);
      } catch (err) {
        setIsConnected(false);
        setError(err instanceof Error ? err.message : 'Connection failed');
      } finally {
        setIsLoading(false);
      }
    };

    testConnection();
  }, []);

  return {
    isConnected,
    isLoading,
    error,
    client: apiClient,
  };
}

export function useDyadApps() {
  const [apps, setApps] = useState<App[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadApps = useCallback(async () => {
    try {
      setIsLoading(true);
      const appList = await apiClient.listApps();
      setApps(appList);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load apps');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const createApp = useCallback(async (name: string, template?: string) => {
    try {
      const newApp = await apiClient.createApp({ name, template });
      setApps(prev => [newApp, ...prev]);
      return newApp;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create app');
      throw err;
    }
  }, []);

  const deleteApp = useCallback(async (id: number) => {
    try {
      await apiClient.deleteApp(id);
      setApps(prev => prev.filter(app => app.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete app');
      throw err;
    }
  }, []);

  const startApp = useCallback(async (id: number) => {
    try {
      await apiClient.startApp(id);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start app');
      throw err;
    }
  }, []);

  const stopApp = useCallback(async (id: number) => {
    try {
      await apiClient.stopApp(id);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to stop app');
      throw err;
    }
  }, []);

  useEffect(() => {
    loadApps();
  }, [loadApps]);

  return {
    apps,
    isLoading,
    error,
    createApp,
    deleteApp,
    startApp,
    stopApp,
    refreshApps: loadApps,
  };
}

export function useDyadChats(appId?: number) {
  const [chats, setChats] = useState<Chat[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadChats = useCallback(async () => {
    try {
      setIsLoading(true);
      const chatList = await apiClient.listChats(appId);
      setChats(chatList);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load chats');
    } finally {
      setIsLoading(false);
    }
  }, [appId]);

  const createChat = useCallback(async (appId: number, title?: string) => {
    try {
      const newChat = await apiClient.createChat({ appId, title });
      setChats(prev => [newChat, ...prev]);
      return newChat;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create chat');
      throw err;
    }
  }, []);

  const deleteChat = useCallback(async (id: number) => {
    try {
      await apiClient.deleteChat(id);
      setChats(prev => prev.filter(chat => chat.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete chat');
      throw err;
    }
  }, []);

  const addMessage = useCallback(async (chatId: number, content: string, role: 'user' | 'assistant' = 'user') => {
    try {
      const message = await apiClient.addMessage(chatId, { content, role });
      return message;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add message');
      throw err;
    }
  }, []);

  useEffect(() => {
    loadChats();
  }, [loadChats]);

  return {
    chats,
    isLoading,
    error,
    createChat,
    deleteChat,
    addMessage,
    refreshChats: loadChats,
  };
}

export function useDyadModels() {
  const [models, setModels] = useState<LanguageModel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadModels = async () => {
      try {
        const modelList = await apiClient.getAllLanguageModels();
        setModels(modelList);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load models');
      } finally {
        setIsLoading(false);
      }
    };

    loadModels();
  }, []);

  return {
    models,
    isLoading,
    error,
  };
}

export function useDyadStreaming() {
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamContent, setStreamContent] = useState('');
  const [error, setError] = useState<string | null>(null);

  const startStream = useCallback(async (
    chatId: number,
    message: string,
    model: { name: string; provider: string },
    appId: number
  ) => {
    try {
      setIsStreaming(true);
      setStreamContent('');
      setError(null);

      await apiClient.streamChat(
        chatId,
        { message, model, appId },
        (chunk) => {
          if (chunk.done) {
            setIsStreaming(false);
          } else {
            setStreamContent(prev => prev + chunk.content);
          }
        }
      );
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Streaming failed');
      setIsStreaming(false);
    }
  }, []);

  const stopStream = useCallback(() => {
    setIsStreaming(false);
  }, []);

  const resetStream = useCallback(() => {
    setStreamContent('');
    setError(null);
  }, []);

  return {
    isStreaming,
    streamContent,
    error,
    startStream,
    stopStream,
    resetStream,
  };
}
